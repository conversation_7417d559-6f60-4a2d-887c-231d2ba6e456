<!DOCTYPE html>
<html>
<head>
    <title>Create Aquaculture Images</title>
    <style>
        canvas {
            border: 1px solid #ccc;
            margin: 10px;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <h1>Aquaculture Platform Images Generator</h1>
    <div class="container">
        <div>
            <h3>Title Image</h3>
            <canvas id="titleCanvas" width="400" height="60"></canvas>
            <br><button onclick="downloadCanvas('titleCanvas', 'aquaculture_title.png')">Download</button>
        </div>
        
        <div>
            <h3>Water Quality Icon</h3>
            <canvas id="waterQualityCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('waterQualityCanvas', 'water_quality.png')">Download</button>
        </div>
        
        <div>
            <h3>Water Grade Icon</h3>
            <canvas id="waterGradeCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('waterGradeCanvas', 'water_grade.png')">Download</button>
        </div>
        
        <div>
            <h3>Farm Ranking Icon</h3>
            <canvas id="farmRankingCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('farmRankingCanvas', 'farm_ranking.png')">Download</button>
        </div>
        
        <div>
            <h3>Environment Alerts Icon</h3>
            <canvas id="envAlertsCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('envAlertsCanvas', 'env_alerts.png')">Download</button>
        </div>
        
        <div>
            <h3>Water System Icon</h3>
            <canvas id="waterSystemCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('waterSystemCanvas', 'water_system.png')">Download</button>
        </div>
        
        <div>
            <h3>Water Alerts Icon</h3>
            <canvas id="waterAlertsCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('waterAlertsCanvas', 'water_alerts.png')">Download</button>
        </div>
        
        <div>
            <h3>Feed Alerts Icon</h3>
            <canvas id="feedAlertsCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('feedAlertsCanvas', 'feed_alerts.png')">Download</button>
        </div>
        
        <div>
            <h3>Daily Monitor Icon</h3>
            <canvas id="dailyMonitorCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('dailyMonitorCanvas', 'daily_monitor.png')">Download</button>
        </div>
        
        <div>
            <h3>Equipment Maintenance Icon</h3>
            <canvas id="equipmentMaintenanceCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('equipmentMaintenanceCanvas', 'equipment_maintenance.png')">Download</button>
        </div>
        
        <div>
            <h3>System Failure Icon</h3>
            <canvas id="systemFailureCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('systemFailureCanvas', 'system_failure.png')">Download</button>
        </div>
        
        <div>
            <h3>Environment Emergency Icon</h3>
            <canvas id="envEmergencyCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('envEmergencyCanvas', 'env_emergency.png')">Download</button>
        </div>
        
        <div>
            <h3>Compliance Icon</h3>
            <canvas id="complianceCanvas" width="50" height="50"></canvas>
            <br><button onclick="downloadCanvas('complianceCanvas', 'compliance.png')">Download</button>
        </div>
        
        <div>
            <h3>Environment Alerts Chart</h3>
            <canvas id="envAlertsChartCanvas" width="300" height="200"></canvas>
            <br><button onclick="downloadCanvas('envAlertsChartCanvas', 'env_alerts_chart.png')">Download</button>
        </div>
    </div>

    <script>
        // Title Image
        const titleCtx = document.getElementById('titleCanvas').getContext('2d');
        titleCtx.fillStyle = '#1B92FD';
        titleCtx.font = 'bold 24px Arial';
        titleCtx.textAlign = 'center';
        titleCtx.fillText('智慧水产养殖监管平台', 200, 35);

        // Water Quality Icon
        const waterQualityCtx = document.getElementById('waterQualityCanvas').getContext('2d');
        waterQualityCtx.fillStyle = '#4A90E2';
        waterQualityCtx.beginPath();
        waterQualityCtx.arc(25, 25, 20, 0, 2 * Math.PI);
        waterQualityCtx.fill();
        waterQualityCtx.fillStyle = '#FFFFFF';
        waterQualityCtx.font = '12px Arial';
        waterQualityCtx.textAlign = 'center';
        waterQualityCtx.fillText('H2O', 25, 30);

        // Water Grade Icon
        const waterGradeCtx = document.getElementById('waterGradeCanvas').getContext('2d');
        waterGradeCtx.fillStyle = '#50C878';
        waterGradeCtx.fillRect(10, 10, 30, 30);
        waterGradeCtx.fillStyle = '#FFFFFF';
        waterGradeCtx.font = 'bold 16px Arial';
        waterGradeCtx.textAlign = 'center';
        waterGradeCtx.fillText('A', 25, 30);

        // Farm Ranking Icon
        const farmRankingCtx = document.getElementById('farmRankingCanvas').getContext('2d');
        farmRankingCtx.fillStyle = '#FFD700';
        farmRankingCtx.beginPath();
        farmRankingCtx.moveTo(25, 5);
        farmRankingCtx.lineTo(30, 20);
        farmRankingCtx.lineTo(45, 20);
        farmRankingCtx.lineTo(35, 30);
        farmRankingCtx.lineTo(40, 45);
        farmRankingCtx.lineTo(25, 35);
        farmRankingCtx.lineTo(10, 45);
        farmRankingCtx.lineTo(15, 30);
        farmRankingCtx.lineTo(5, 20);
        farmRankingCtx.lineTo(20, 20);
        farmRankingCtx.closePath();
        farmRankingCtx.fill();

        // Environment Alerts Icon
        const envAlertsCtx = document.getElementById('envAlertsCanvas').getContext('2d');
        envAlertsCtx.fillStyle = '#FF6B6B';
        envAlertsCtx.beginPath();
        envAlertsCtx.moveTo(25, 5);
        envAlertsCtx.lineTo(45, 45);
        envAlertsCtx.lineTo(5, 45);
        envAlertsCtx.closePath();
        envAlertsCtx.fill();
        envAlertsCtx.fillStyle = '#FFFFFF';
        envAlertsCtx.font = 'bold 20px Arial';
        envAlertsCtx.textAlign = 'center';
        envAlertsCtx.fillText('!', 25, 35);

        // Water System Icon
        const waterSystemCtx = document.getElementById('waterSystemCanvas').getContext('2d');
        waterSystemCtx.fillStyle = '#4ECDC4';
        waterSystemCtx.fillRect(5, 15, 40, 20);
        waterSystemCtx.fillStyle = '#2E8B57';
        waterSystemCtx.fillRect(15, 5, 20, 10);
        waterSystemCtx.fillRect(15, 35, 20, 10);

        // Water Alerts Icon
        const waterAlertsCtx = document.getElementById('waterAlertsCanvas').getContext('2d');
        waterAlertsCtx.fillStyle = '#FF9500';
        waterAlertsCtx.beginPath();
        waterAlertsCtx.arc(25, 25, 20, 0, 2 * Math.PI);
        waterAlertsCtx.fill();
        waterAlertsCtx.fillStyle = '#FFFFFF';
        waterAlertsCtx.font = 'bold 14px Arial';
        waterAlertsCtx.textAlign = 'center';
        waterAlertsCtx.fillText('⚠', 25, 30);

        // Feed Alerts Icon
        const feedAlertsCtx = document.getElementById('feedAlertsCanvas').getContext('2d');
        feedAlertsCtx.fillStyle = '#8B4513';
        feedAlertsCtx.fillRect(10, 20, 30, 15);
        feedAlertsCtx.fillStyle = '#D2691E';
        feedAlertsCtx.beginPath();
        feedAlertsCtx.arc(15, 15, 3, 0, 2 * Math.PI);
        feedAlertsCtx.arc(25, 12, 3, 0, 2 * Math.PI);
        feedAlertsCtx.arc(35, 15, 3, 0, 2 * Math.PI);
        feedAlertsCtx.fill();

        // Daily Monitor Icon
        const dailyMonitorCtx = document.getElementById('dailyMonitorCanvas').getContext('2d');
        dailyMonitorCtx.fillStyle = '#9B59B6';
        dailyMonitorCtx.fillRect(5, 5, 40, 30);
        dailyMonitorCtx.fillStyle = '#FFFFFF';
        dailyMonitorCtx.fillRect(10, 10, 30, 20);
        dailyMonitorCtx.fillStyle = '#9B59B6';
        dailyMonitorCtx.fillRect(15, 15, 20, 2);
        dailyMonitorCtx.fillRect(15, 20, 15, 2);
        dailyMonitorCtx.fillRect(15, 25, 10, 2);

        // Equipment Maintenance Icon
        const equipmentMaintenanceCtx = document.getElementById('equipmentMaintenanceCanvas').getContext('2d');
        equipmentMaintenanceCtx.fillStyle = '#34495E';
        equipmentMaintenanceCtx.fillRect(15, 10, 20, 30);
        equipmentMaintenanceCtx.fillStyle = '#E74C3C';
        equipmentMaintenanceCtx.fillRect(20, 15, 10, 5);
        equipmentMaintenanceCtx.fillStyle = '#F39C12';
        equipmentMaintenanceCtx.fillRect(20, 25, 10, 5);
        equipmentMaintenanceCtx.fillStyle = '#27AE60';
        equipmentMaintenanceCtx.fillRect(20, 35, 10, 5);

        // System Failure Icon
        const systemFailureCtx = document.getElementById('systemFailureCanvas').getContext('2d');
        systemFailureCtx.fillStyle = '#E74C3C';
        systemFailureCtx.beginPath();
        systemFailureCtx.arc(25, 25, 20, 0, 2 * Math.PI);
        systemFailureCtx.fill();
        systemFailureCtx.strokeStyle = '#FFFFFF';
        systemFailureCtx.lineWidth = 3;
        systemFailureCtx.beginPath();
        systemFailureCtx.moveTo(15, 15);
        systemFailureCtx.lineTo(35, 35);
        systemFailureCtx.moveTo(35, 15);
        systemFailureCtx.lineTo(15, 35);
        systemFailureCtx.stroke();

        // Environment Emergency Icon
        const envEmergencyCtx = document.getElementById('envEmergencyCanvas').getContext('2d');
        envEmergencyCtx.fillStyle = '#C0392B';
        envEmergencyCtx.fillRect(5, 5, 40, 40);
        envEmergencyCtx.fillStyle = '#FFFFFF';
        envEmergencyCtx.font = 'bold 24px Arial';
        envEmergencyCtx.textAlign = 'center';
        envEmergencyCtx.fillText('⚡', 25, 32);

        // Compliance Icon
        const complianceCtx = document.getElementById('complianceCanvas').getContext('2d');
        complianceCtx.fillStyle = '#27AE60';
        complianceCtx.beginPath();
        complianceCtx.arc(25, 25, 20, 0, 2 * Math.PI);
        complianceCtx.fill();
        complianceCtx.strokeStyle = '#FFFFFF';
        complianceCtx.lineWidth = 3;
        complianceCtx.beginPath();
        complianceCtx.moveTo(15, 25);
        complianceCtx.lineTo(22, 32);
        complianceCtx.lineTo(35, 18);
        complianceCtx.stroke();

        // Environment Alerts Chart
        const envAlertsChartCtx = document.getElementById('envAlertsChartCanvas').getContext('2d');
        envAlertsChartCtx.fillStyle = '#2C3E50';
        envAlertsChartCtx.fillRect(0, 0, 300, 200);
        
        // Draw bars
        const barData = [60, 80, 45, 90, 70, 55, 85, 40, 75, 65];
        const barWidth = 25;
        const barSpacing = 5;
        
        for (let i = 0; i < barData.length; i++) {
            const x = 20 + i * (barWidth + barSpacing);
            const height = barData[i] * 1.5;
            const y = 180 - height;
            
            envAlertsChartCtx.fillStyle = i % 3 === 0 ? '#E74C3C' : i % 3 === 1 ? '#F39C12' : '#27AE60';
            envAlertsChartCtx.fillRect(x, y, barWidth, height);
        }
        
        envAlertsChartCtx.fillStyle = '#FFFFFF';
        envAlertsChartCtx.font = '14px Arial';
        envAlertsChartCtx.fillText('环境预警统计图表', 10, 20);

        function downloadCanvas(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
