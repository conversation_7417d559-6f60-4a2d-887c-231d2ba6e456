html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  overflow: hidden;
}
.titleText {
  font-size: 18px;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  font-weight: 600;
  color: #1565c0;
  text-shadow: 0 1px 3px rgba(21, 101, 192, 0.3);
  letter-spacing: 0.5px;
}
.main {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}
.main .header {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(21, 101, 192, 0.2);
  border-radius: 50px;
  box-shadow: 0 8px 32px rgba(21, 101, 192, 0.15);
  padding: 0 30px;
  z-index: 1000;
}
.main .header .dateBox {
  width: auto;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}
.main .header .dateBox span {
  font-size: 14px;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  font-weight: 500;
  color: #1565c0;
  letter-spacing: 0.5px;
}
.main .content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  pointer-events: none;
}

/* Full-page map container */
.main .content .fullPageMap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 1;
  pointer-events: all;
}

.main .content .fullPageMap #container {
  width: 100% !important;
  height: 100vh !important;
  border: none;
  border-radius: 0;
  background-color: rgba(50,72,106,0.2) !important;
  position: relative !important;
}
/* Floating Left Panel */
.main .content .leftBox {
  position: fixed;
  top: 120px;
  left: 20px;
  width: 320px;
  max-height: calc(100vh - 140px);
  z-index: 200;
  pointer-events: all;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
}

.main .content .leftBox .pingfen {
  width: 100%;
  height: 200px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(21, 101, 192, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(21, 101, 192, 0.15);
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
}
.main .content .leftBox .pingfen .pfcontent {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 15px;
}
.main .content .leftBox .pingfen .pfcontent .leftBox {
  width: 84px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.main .content .leftBox .pingfen .pfcontent .rightBox {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
  font-size: 36px;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
}
.comLine {
  width: 1px;
  height: 180px;
  border: 1px solid rgba(62, 99, 151, 1);
  border-right: none;
  border-top: none;
  border-bottom: none;
}
.smdate {
  font-size: 12px;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  color: #1565c0;
  position: absolute;
  left: 25px;
  z-index: 10;
}
.smtext {
  font-size: 14px;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  color: #1565c0;
  margin: 0 10px;
}
.main .content .leftBox .pingfen .pfcontent .leftBox span {
  margin-top: 10px;
  font-size: 14px;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  color: rgba(55, 180, 228, 1);
}
.main .content .pfTitle {
  margin: 0;
  padding: 15px;
  display: flex;
  align-items: center;
  background: rgba(21, 101, 192, 0.05);
  border-bottom: 1px solid rgba(21, 101, 192, 0.1);
  font-size: 14px;
  font-weight: 600;
  color: #1565c0;
}
.main .content .leftBox .pingfen .pfTitle > img {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}
.main .content .leftBox .pingfen > div {
  height: 100%;
  border: none;
}
.main .content .leftBox .paimin {
  width: 100%;
  height: 200px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(21, 101, 192, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(21, 101, 192, 0.15);
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
}
.main .content .leftBox .paimin > div {
  height: 100%;
  border: none;
  padding: 15px;
}
/* 安全隐患 */
.main .content .leftBox .top10 {
  width: 100%;
  height: 320px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(21, 101, 192, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(21, 101, 192, 0.15);
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
}
.main .content .leftBox .top10 > div {
  height: 100%;
  border: none;
  padding: 15px;
}
/* Floating Right Panel */
.main .content .rightBox {
  position: fixed;
  top: 120px;
  right: 20px;
  width: 320px;
  max-height: calc(100vh - 140px);
  z-index: 200;
  pointer-events: all;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
}
.main .content .middleBox .baiduMap #container {
  height: 705px;
  border: 1px solid rgba(27, 146, 253, 0.5);
}
/* Floating Bottom Panel */
.main .content .middleBox {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  max-width: 1200px;
  z-index: 200;
  pointer-events: all;
}

.main .content .middleBox .tongjiList {
  height: 160px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(21, 101, 192, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(21, 101, 192, 0.15);
  padding: 15px;
  box-sizing: border-box;
}
.main .content .middleBox .tongjiList > div {
  height: 203px;
  border: 1px solid rgba(27, 146, 253, 0.5);
  display: flex;
  align-items: center;
}

.main .content .rightBox .weihutj {
  height: 200px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(21, 101, 192, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(21, 101, 192, 0.15);
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
}
.main .content .rightBox .weihutj > div {
  height: 100%;
  width: 100%;
  border: none;
  padding: 15px;
}
.main .content .rightBox .guzhangtj,
.main .content .rightBox .huojingtj,
.main .content .rightBox .zhifatj {
  height: 180px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(21, 101, 192, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(21, 101, 192, 0.15);
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
}
.pfcontent .leftBox .sbwx {
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-image: url(../img/sbwx.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.wbfgl {
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-image: url(../img/wbfg.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.flexcolom {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.flexrow {
  display: flex;
  justify-content: center;
  align-items: center;
}
.animText {
  font-size: 30px;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  color: #880e23;
}
.sbwxtext{
  color: #880e23;
}
#FontScroll ul {
  list-style: none;
  width: 230px;
  font-size: 14px;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
  line-height: 28px;
}
#FontScroll ul li a{
  display: flex;
  justify-content: space-between;
}
#FontScroll ul li .leftSpan{
    display: block;
    width: 128px;
    text-align: right;
  }
  .anchorBL{
    display:none;
    }

/* Light Blue Theme Enhancements */
.main .content .leftBox .pingfen,
.main .content .leftBox .paimin,
.main .content .leftBox .top10,
.main .content .rightBox .weihutj,
.main .content .rightBox .guzhangtj,
.main .content .rightBox .huojingtj,
.main .content .rightBox .zhifatj,
.main .content .middleBox .tongjiList {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.main .content .leftBox .pingfen:hover,
.main .content .leftBox .paimin:hover,
.main .content .leftBox .top10:hover,
.main .content .rightBox .weihutj:hover,
.main .content .rightBox .guzhangtj:hover,
.main .content .rightBox .huojingtj:hover,
.main .content .rightBox .zhifatj:hover,
.main .content .middleBox .tongjiList:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(21, 101, 192, 0.25);
  border-color: rgba(21, 101, 192, 0.4);
}

/* Highlight field block segments */
.main .content .leftBox .pingfen::before,
.main .content .leftBox .paimin::before,
.main .content .leftBox .top10::before,
.main .content .rightBox .weihutj::before,
.main .content .rightBox .guzhangtj::before,
.main .content .rightBox .huojingtj::before,
.main .content .rightBox .zhifatj::before,
.main .content .middleBox .tongjiList::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(25, 118, 210, 0.15), transparent);
  animation: shimmer 4s infinite;
  border-radius: 16px;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Enhanced borders for all panels */
.main .content .leftBox .pingfen > div,
.main .content .leftBox .paimin > div,
.main .content .leftBox .top10 > div,
.main .content .middleBox .baiduMap #container,
.main .content .middleBox .tongjiList > div,
.main .content .rightBox .weihutj > div,
.main .content .rightBox .guzhangtj > div,
.main .content .rightBox .huojingtj > div,
.main .content .rightBox .zhifatj > div {
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 6px;
  background: transparent;
}

/* Glowing effect for title text */
.titleText {
  position: relative;
}

.titleText::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  border-radius: 4px;
  z-index: -1;
}

/* Enhanced scrolling list */
#FontScroll ul li a {
  transition: all 0.3s ease;
  padding: 5px 10px;
  border-radius: 4px;
}

#FontScroll ul li a:hover {
  background: rgba(0, 212, 255, 0.1);
  transform: translateX(5px);
}

/* Modern button-like elements */
.pfTitle img {
  filter: drop-shadow(0 0 5px rgba(0, 212, 255, 0.5));
}

/* Enhanced map container */
.main .content .middleBox .baiduMap #container {
  border-radius: 6px;
  overflow: hidden;
}

/* Subtle animation for panels */
.main .content .leftBox .pingfen,
.main .content .leftBox .paimin,
.main .content .leftBox .top10,
.main .content .middleBox .baiduMap,
.main .content .middleBox .tongjiList,
.main .content .rightBox .weihutj,
.main .content .rightBox .guzhangtj,
.main .content .rightBox .huojingtj,
.main .content .rightBox .zhifatj {
  transition: all 0.3s ease;
}

.main .content .leftBox .pingfen:hover,
.main .content .leftBox .paimin:hover,
.main .content .leftBox .top10:hover,
.main .content .middleBox .baiduMap:hover,
.main .content .middleBox .tongjiList:hover,
.main .content .rightBox .weihutj:hover,
.main .content .rightBox .guzhangtj:hover,
.main .content .rightBox .huojingtj:hover,
.main .content .rightBox .zhifatj:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4),
              inset 0 1px 0 rgba(255, 255, 255, 0.15),
              0 0 20px rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.6);
}

/* Enhanced text styling */
.smtext, .smdate {
  color: #1565c0;
  text-shadow: 0 1px 2px rgba(21, 101, 192, 0.2);
}

/* Update list text colors */
#FontScroll ul {
  list-style: none;
  width: 230px;
  font-size: 14px;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  color: #1565c0;
  line-height: 28px;
}

#FontScroll ul li a:hover {
  background: rgba(25, 118, 210, 0.1);
  transform: translateX(5px);
}

/* Modern scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(187, 222, 251, 0.3);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #1976d2, #1565c0);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #42a5f5, #1976d2);
}

/* Remove old borders and update with light theme */
.main .content .leftBox .pingfen > div,
.main .content .leftBox .paimin > div,
.main .content .leftBox .top10 > div,
.main .content .middleBox .tongjiList > div,
.main .content .rightBox .weihutj > div,
.main .content .rightBox .guzhangtj > div,
.main .content .rightBox .huojingtj > div,
.main .content .rightBox .zhifatj > div {
  border: none;
  border-radius: 12px;
  background: transparent;
  height: auto;
  padding: 0;
  margin: 0;
}

/* Ensure chart containers have proper sizing */
[id^="echarts"], [id^="echart"] {
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

/* Fix specific chart container heights */
#echartAqpf {
  width: 105px !important;
  height: 105px !important;
  margin: 0 auto;
  display: block;
}

#echartsEnvAlerts {
  width: 100% !important;
  height: 270px !important;
  min-height: 270px;
}

#echartsxfjs, #echartsldbj, #echartsjgbj, #echartsrcxj {
  width: 100% !important;
  height: 130px !important;
  min-height: 130px;
}

#echartWbtj {
  width: 105px !important;
  height: 105px !important;
  margin: 0 auto;
  display: block;
}

#echartsgztj, #echartshjtj {
  width: 100% !important;
  height: 130px !important;
  min-height: 130px;
}

/* Fix right panel chart containers */
.main .content .rightBox .guzhangtj > div,
.main .content .rightBox .huojingtj > div,
.main .content .rightBox .zhifatj > div {
  height: 100%;
  border: none;
  padding: 15px;
}

#echartszfjgtj {
  width: 100% !important;
  height: 130px !important;
  min-height: 130px;
}

/* Fix text overlapping issues */
.main .content .middleBox .tongjiList .col-md-3 {
  position: relative;
  overflow: hidden;
}

.main .content .middleBox .tongjiList .pfTitle {
  position: relative;
  z-index: 5;
  background: rgba(255, 255, 255, 0.9);
  margin: 0;
  padding: 8px 15px;
  border-radius: 8px 8px 0 0;
}

/* Ensure charts don't overlap with titles */
.main .content .middleBox .tongjiList [id^="echarts"] {
  margin-top: 5px;
}

/* Fix right panel layout */
.main .content .rightBox > div {
  margin-bottom: 15px;
}

/* Ensure proper spacing for all chart containers */
[id^="echarts"], [id^="echart"] {
  position: relative;
  z-index: 1;
}

/* Fix map container to ensure it's visible */
#container {
  position: relative !important;
  z-index: 1 !important;
}

/* Ensure all floating panels are properly visible */
.main .content .leftBox,
.main .content .rightBox,
.main .content .middleBox {
  visibility: visible;
  opacity: 1;
}

/* Fix potential layout issues */
.main .content .leftBox > div,
.main .content .rightBox > div {
  width: 100%;
  box-sizing: border-box;
}

/* Ensure chart containers are properly sized */
.main .content .leftBox .pingfen .pfcontent,
.main .content .rightBox .weihutj .pfcontent {
  height: calc(100% - 50px);
  overflow: visible;
}

/* Fix middle box layout */
.main .content .middleBox .tongjiList > div {
  height: auto;
  min-height: 130px;
  border: none;
  display: flex;
  align-items: stretch;
  justify-content: space-between;
}

.main .content .middleBox .tongjiList .col-md-3 {
  flex: 1;
  margin: 0 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
