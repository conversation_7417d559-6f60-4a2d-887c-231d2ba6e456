//日常监测统计
var myChart = echarts.init(document.getElementById('echartsrcxj'))
echarts.registerTheme('aquaculture', {
    color: ['#1976d2', '#42a5f5', '#64b5f6', '#90caf9', '#bbdefb'],
    backgroundColor: 'transparent',
    textStyle: {
        color: '#1565c0'
    },
    title: {
        textStyle: {
            color: '#1565c0'
        }
    },
    line: {
        itemStyle: {
            borderWidth: 2
        },
        lineStyle: {
            width: 3
        },
        symbolSize: 6,
        symbol: 'circle',
        smooth: true
    },
    radar: {
        itemStyle: {
            borderWidth: 2
        },
        lineStyle: {
            width: 3
        },
        symbolSize: 6,
        symbol: 'circle',
        smooth: true
    },
    bar: {
        itemStyle: {
            barBorderWidth: 0,
            barBorderColor: '#ccc'
        }
    },
    pie: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc'
        }
    },
    scatter: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc'
        }
    },
    boxplot: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc'
        }
    },
    parallel: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc'
        }
    },
    sankey: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc'
        }
    },
    funnel: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc'
        }
    },
    gauge: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc'
        }
    },
    candlestick: {
        itemStyle: {
            color: '#00d4ff',
            color0: '#4fc3f7',
            borderColor: '#00d4ff',
            borderColor0: '#4fc3f7',
            borderWidth: 1
        }
    },
    graph: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc'
        },
        lineStyle: {
            width: 1,
            color: '#aaa'
        },
        symbolSize: 6,
        symbol: 'circle',
        smooth: true,
        color: ['#00d4ff', '#4fc3f7', '#81c784', '#ffb74d', '#f06292'],
        label: {
            color: '#e0e6ed'
        }
    },
    map: {
        itemStyle: {
            areaColor: '#eee',
            borderColor: '#444',
            borderWidth: 0.5
        },
        label: {
            color: '#000'
        },
        emphasis: {
            itemStyle: {
                areaColor: 'rgba(255,215,0,0.8)',
                borderColor: '#444',
                borderWidth: 1
            },
            label: {
                color: 'rgb(100,0,0)'
            }
        }
    },
    geo: {
        itemStyle: {
            areaColor: '#eee',
            borderColor: '#444',
            borderWidth: 0.5
        },
        label: {
            color: '#000'
        },
        emphasis: {
            itemStyle: {
                areaColor: 'rgba(255,215,0,0.8)',
                borderColor: '#444',
                borderWidth: 1
            },
            label: {
                color: 'rgb(100,0,0)'
            }
        }
    },
    categoryAxis: {
        axisLine: {
            show: true,
            lineStyle: {
                color: '#1976d2'
            }
        },
        axisTick: {
            show: true,
            lineStyle: {
                color: '#1976d2'
            }
        },
        axisLabel: {
            show: true,
            color: '#1565c0'
        },
        splitLine: {
            show: false,
            lineStyle: {
                color: ['rgba(25, 118, 210, 0.1)']
            }
        },
        splitArea: {
            show: false,
            areaStyle: {
                color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
            }
        }
    },
    valueAxis: {
        axisLine: {
            show: true,
            lineStyle: {
                color: '#1976d2'
            }
        },
        axisTick: {
            show: true,
            lineStyle: {
                color: '#1976d2'
            }
        },
        axisLabel: {
            show: true,
            color: '#1565c0'
        },
        splitLine: {
            show: true,
            lineStyle: {
                color: ['rgba(25, 118, 210, 0.1)']
            }
        },
        splitArea: {
            show: false,
            areaStyle: {
                color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
            }
        }
    },
    logAxis: {
        axisLine: {
            show: true,
            lineStyle: {
                color: '#00d4ff'
            }
        },
        axisTick: {
            show: true,
            lineStyle: {
                color: '#00d4ff'
            }
        },
        axisLabel: {
            show: true,
            color: '#e0e6ed'
        },
        splitLine: {
            show: true,
            lineStyle: {
                color: ['rgba(0, 212, 255, 0.1)']
            }
        },
        splitArea: {
            show: false,
            areaStyle: {
                color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
            }
        }
    },
    timeAxis: {
        axisLine: {
            show: true,
            lineStyle: {
                color: '#00d4ff'
            }
        },
        axisTick: {
            show: true,
            lineStyle: {
                color: '#00d4ff'
            }
        },
        axisLabel: {
            show: true,
            color: '#e0e6ed'
        },
        splitLine: {
            show: true,
            lineStyle: {
                color: ['rgba(0, 212, 255, 0.1)']
            }
        },
        splitArea: {
            show: false,
            areaStyle: {
                color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
            }
        }
    },
    toolbox: {
        color: ['#00d4ff', '#4fc3f7', '#81c784', '#ffb74d'],
        effectColor: '#ff6b6b',
        emphasis: {
            color: ['#00e6ff', '#5fd4f8', '#92d895', '#ffcc6f']
        }
    },
    legend: {
        textStyle: {
            color: '#e0e6ed'
        }
    },
    tooltip: {
        axisPointer: {
            lineStyle: {
                color: '#00d4ff',
                width: 1
            },
            crossStyle: {
                color: '#00d4ff',
                width: 1
            }
        }
    },
    timeline: {
        lineStyle: {
            color: '#00d4ff',
            width: 1
        },
        itemStyle: {
            color: '#00d4ff',
            borderWidth: 1
        },
        controlStyle: {
            color: '#00d4ff',
            borderColor: '#00d4ff',
            borderWidth: 0.5
        },
        checkpointStyle: {
            color: '#4fc3f7',
            borderColor: 'rgba(79, 195, 247, 0.3)'
        },
        label: {
            color: '#e0e6ed'
        },
        emphasis: {
            itemStyle: {
                color: '#4fc3f7'
            },
            controlStyle: {
                color: '#00d4ff',
                borderColor: '#00d4ff',
                borderWidth: 0.5
            },
            label: {
                color: '#e0e6ed'
            }
        }
    },
    visualMap: {
        color: ['#ff6b6b', '#ffb74d', '#4fc3f7']
    },
    dataZoom: {
        backgroundColor: 'rgba(47,69,84,0)',
        dataBackgroundColor: 'rgba(255,255,255,0.3)',
        fillerColor: 'rgba(167,183,204,0.4)',
        handleColor: '#a7b7cc',
        handleSize: '100%',
        textStyle: {
            color: '#333'
        }
    },
    markPoint: {
        label: {
            color: '#e0e6ed'
        },
        emphasis: {
            label: {
                color: '#e0e6ed'
            }
        }
    }
});

myChart = echarts.init(document.getElementById('echartsrcxj'), 'aquaculture')

option = {
  // title: {
  //     text: '动态数据',
  // },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: 'rgba(62, 99, 151, 1)'
      }
    }
  },
  legend: {
    data: ['监测次数', '监测覆盖率']
  },

  xAxis: [
    {
      type: 'category',
      boundaryGap: true,
      data: (function() {
        var now = new Date()
        var res = []
        var len = 8
        while (len--) {
          res.unshift(now.getDate())
          now = new Date(now - 86400000)
        }
        return res
      })()
    },
    {
      type: 'category',
      boundaryGap: true,
      data: (function() {
        var res = []
        var len = 0
        while (len--) {
          res.push(8 - len - 1)
        }
        return res
      })()
    }
  ],
  yAxis: [
    {
      type: 'value',
      scale: true,
      name: '单位',
      max: 30,
      min: 0,
      boundaryGap: [0.2, 0.2]
    },
    {
      type: 'value',
      scale: true,
      name: '单位',
      max: 10,
      min: 0,
      boundaryGap: [0.2, 0.2]
    }
  ],
  series: [
    {
      name: '监测覆盖率',
      type: 'bar',
      xAxisIndex: 1,
      yAxisIndex: 1,
      data: (function() {
        var res = []
        var len = 8
        while (len--) {
          res.push(Math.round(Math.random() * 10))
        }
        return res
      })()
    },
    {
      name: '监测次数',
      type: 'line',
      data: (function() {
        var res = []
        var len = 0
        while (len < 8) {
          res.push((Math.random() * 10 + 5).toFixed(1) - 0)
          len++
        }
        return res
      })()
    }
  ]
}

myChart.setOption(option)
//水质预警统计
var myChart1 = echarts.init(document.getElementById('echartsldbj'), 'aquaculture')

option1 = {
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: (function() {
      var now = new Date()
      var res = []
      var len = 8
      while (len--) {
        res.unshift(now.getDate())
        now = new Date(now - 86400000)
      }
      return res
    })()
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      data: [45, 60, 42, 56, 30, 42, 56, 30],
      type: 'line',
      areaStyle: {}
    }
  ]
}

myChart1.setOption(option1)

var myChart2 = echarts.init(document.getElementById('echartsjgbj'), 'aquaculture')

option2 = {
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: (function() {
      var now = new Date()
      var res = []
      var len = 7
      while (len--) {
        res.unshift(now.getDate())
        now = new Date(now - 86400000)
      }
      return res
    })()
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      data: [55, 32, 77, 85, 23, 59, 88],
      type: 'line',
      areaStyle: {}
    }
  ]
}

myChart2.setOption(option2)

//水循环系统
var myChart3 = echarts.init(document.getElementById('echartsxfjs'), 'aquaculture')

option3 = {
  xAxis: {
    type: 'category',
    data: ['增氧机故障', '过滤器堵塞', '水泵异常']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      data: [15, 45, 30],
      type: 'bar'
    }
  ]
}

// 使用刚指定的配置项和数据显示图表。
myChart3.setOption(option3)

//系统故障统计

var myChart4 = echarts.init(document.getElementById('echartsgztj'), 'aquaculture')

option4 = {
  // title: {
  //     text: '动态数据',
  // },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: 'rgba(62, 99, 151, 1)'
      }
    }
  },
  legend: {
    data: ['设备故障数量', '故障处理及时率']
  },

  xAxis: [
    {
      type: 'category',
      boundaryGap: true,
      data: (function() {
        var now = new Date()
        var res = []
        var len = 8
        while (len--) {
          res.unshift(now.getDate())
          now = new Date(now - 86400000)
        }
        return res
      })()
    },
    {
      type: 'category',
      boundaryGap: true,
      data: (function() {
        var res = []
        var len = 0
        while (len--) {
          res.push(8 - len - 1)
        }
        return res
      })()
    }
  ],
  yAxis: [
    {
      type: 'value',
      scale: true,
      name: '数量',
      max: 30,
      min: 0,
      boundaryGap: [0.2, 0.2]
    },
    {
      type: 'value',
      scale: true,
      name: '%',
      max: 100,
      min: 0,
      boundaryGap: [0.2, 0.2]
    }
  ],
  series: [
    {
      name: '设备故障数量',
      type: 'bar',
      xAxisIndex: 1,
      yAxisIndex: 1,
      data: (function() {
        var res = []
        var len = 8
        while (len--) {
          res.push(Math.round(Math.random() * 100))
        }
        return res
      })()
    },
    {
      name: '故障处理及时率',
      type: 'line',
      data: (function() {
        var res = []
        var len = 0
        while (len < 8) {
          res.push((Math.random() * 10 + 5).toFixed(1) - 0)
          len++
        }
        return res
      })()
    }
  ]
}

// 使用刚指定的配置项和数据显示图表。
myChart4.setOption(option4)

//环境应急统计

var myChart5 = echarts.init(document.getElementById('echartshjtj'), 'aquaculture')
var colors = ['#5793f3', '#d14a61', '#675bba']

option5 = {
  color: colors,

  tooltip: {
    trigger: 'none',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: ['环境异常数量', '应急处理及时率']
  },
  grid: {
    top: 50,
    bottom: 50
  },
  xAxis: [
    {
      type: 'category',
      axisTick: {
        alignWithLabel: true
      },
      axisLine: {
        onZero: false,
        lineStyle: {
          color: colors[1]
        }
      },
      axisPointer: {
        label: {
          formatter: function(params) {
            return (
              '环境异常数量  ' +
              params.value +
              (params.seriesData.length ? '：' + params.seriesData[0].data : '')
            )
          }
        }
      },
      data: (function() {
        var now = new Date()
        var res = []
        var len = 8
        while (len--) {
          res.unshift(now.getDate())
          now = new Date(now - 86400000)
        }
        return res
      })()
    },
    {
      type: 'category',
      axisTick: {
        alignWithLabel: true
      },

      axisPointer: {
        label: {
          formatter: function(params) {
            return (
              '应急处理及时率  ' +
              params.value +
              (params.seriesData.length ? '：' + params.seriesData[0].data : '')
            )
          }
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      max: 90,
      min: 0
    }
  ],
  series: [
    {
      name: '环境异常数量',
      type: 'line',
      xAxisIndex: 1,
      smooth: true,
      data: [1.2, 3.5, 4.8, 12.3, 15.6, 8.9, 5.2, 7.8, 6.3, 9.1, 3.2, 1.8]
    },
    {
      name: '应急处理及时率',
      type: 'line',
      smooth: true,
      data: [
        85.2,
        88.9,
        92.1,
        87.3,
        90.5,
        94.2,
        89.6,
        91.8,
        93.4,
        88.7,
        90.3,
        95.1
      ]
    }
  ]
}

myChart5.setOption(option5)

//合规监管统计
var myChart6 = echarts.init(document.getElementById('echartszfjgtj'), 'aquaculture')

option6 = {
    color: ['#3398DB'],
    tooltip : {
        trigger: 'axis',
        axisPointer : {            // 坐标轴指示器，坐标轴触发有效
            type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
        }
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis : [
        {
            type : 'category',
            data : ['水质检测', '饲料检查', '环保审核', '养殖许可', '产品认证'],
            axisTick: {
                alignWithLabel: true
            }
        }
    ],
    yAxis : [
        {
            type : 'value'
        }
    ],
    series : [
        {
            type:'bar',
            barWidth: '60%',
            data:[25, 38, 22, 35, 18]
        }
    ]
};


// 使用刚指定的配置项和数据显示图表。
myChart6.setOption(option6)

//水质评分
var myChart7 = echarts.init(document.getElementById('echartAqpf'))

option7 = {
    title: {
        text: '92',
        x: 'center',
        y: 'center',
        textStyle: {
            fontWeight: 'normal',
            color: '#ffffff',
            fontSize: '30'
        }
    },
    color: ['rgba(176, 212, 251, 1)'],


    series: [{
        name: 'Line 1',
        type: 'pie',
        clockWise: true,
        radius: ['100%', '85%'],
        itemStyle: {
            normal: {
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                }
            }
        },
        hoverAnimation: false,
        data: [{
            value: 92,
            name: '01',
            itemStyle: {
                normal: {
                    color: { // 完成的圆环的颜色
                        colorStops: [{
                            offset: 0,
                            color: '#00cefc' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: '#367bec' // 100% 处的颜色
                        }]
                    },
                    label: {
                        show: false
                    },
                    labelLine: {
                        show: false
                    }
                }
            }
        }, {
            name: '02',
            value: 8
        }]
    }]
}


// 使用刚指定的配置项和数据显示图表。
myChart7.setOption(option7)

//设备维护统计
var myChart8 = echarts.init(document.getElementById('echartWbtj'))

option8 = {
    title: {
        text: '85%',
        x: 'center',
        y: 'center',
        textStyle: {
            fontWeight: 'normal',
            color: '#FFFFFF',
            fontSize: '30'
        }
    },
    color: ['rgba(176, 212, 251, 0.5)'],


    series: [{
        name: 'Line 1',
        type: 'pie',
        clockWise: true,
        radius: ['90%', '75%'],
        itemStyle: {
            normal: {
                label: {
                    show: false
                },
                labelLine: {
                    show: false
                }
            }
        },
        hoverAnimation: false,
        data: [{
            value: 85,
            name: '01',
            itemStyle: {
                normal: {
                    color: { // 完成的圆环的颜色
                        colorStops: [{
                            offset: 0,
                            color: '#00cefc' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: '#367bec' // 100% 处的颜色
                        }]
                    },
                    label: {
                        show: false
                    },
                    labelLine: {
                        show: false
                    }
                }
            }
        }, {
            name: '02',
            value: 15
        }]
    }]
}


// 使用刚指定的配置项和数据显示图表。
myChart8.setOption(option8)

//环境预警TOP10图表
var myChart9 = echarts.init(document.getElementById('echartsEnvAlerts'), 'aquaculture')

option9 = {
    title: {
        text: '环境预警分析',
        left: 'center',
        top: 10,
        textStyle: {
            color: '#1565c0',
            fontSize: 16,
            fontWeight: 'bold'
        }
    },
    tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#1976d2',
        borderWidth: 1,
        textStyle: {
            color: '#1565c0'
        }
    },
    legend: {
        data: ['水质异常', '设备故障', '环境超标'],
        top: 35,
        textStyle: {
            color: '#1565c0',
            fontSize: 12
        }
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '25%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月'],
        axisLine: {
            lineStyle: {
                color: '#1976d2'
            }
        },
        axisLabel: {
            color: '#1565c0',
            fontSize: 10
        }
    },
    yAxis: {
        type: 'value',
        axisLine: {
            lineStyle: {
                color: '#1976d2'
            }
        },
        axisLabel: {
            color: '#1565c0',
            fontSize: 10
        },
        splitLine: {
            lineStyle: {
                color: 'rgba(25, 118, 210, 0.2)'
            }
        }
    },
    series: [
        {
            name: '水质异常',
            type: 'bar',
            data: [12, 8, 15, 6, 9, 11, 7, 13, 10, 5],
            itemStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#ff6b6b'
                    }, {
                        offset: 1, color: '#ee5a52'
                    }]
                }
            }
        },
        {
            name: '设备故障',
            type: 'bar',
            data: [8, 12, 9, 14, 7, 6, 11, 8, 13, 9],
            itemStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#ffa726'
                    }, {
                        offset: 1, color: '#ff9800'
                    }]
                }
            }
        },
        {
            name: '环境超标',
            type: 'line',
            data: [5, 7, 4, 8, 6, 9, 5, 7, 6, 4],
            lineStyle: {
                color: '#42a5f5',
                width: 3
            },
            itemStyle: {
                color: '#42a5f5'
            },
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(66, 165, 245, 0.3)'
                    }, {
                        offset: 1, color: 'rgba(66, 165, 245, 0.1)'
                    }]
                }
            }
        }
    ]
}

myChart9.setOption(option9)

// Resize all charts when window resizes
window.addEventListener('resize', function() {
    myChart.resize();
    myChart1.resize();
    myChart2.resize();
    myChart3.resize();
    myChart4.resize();
    myChart5.resize();
    myChart6.resize();
    myChart7.resize();
    myChart8.resize();
    myChart9.resize();
});

// Initialize chart resizing after a delay to ensure proper rendering
setTimeout(function() {
    myChart.resize();
    myChart1.resize();
    myChart2.resize();
    myChart3.resize();
    myChart4.resize();
    myChart5.resize();
    myChart6.resize();
    myChart7.resize();
    myChart8.resize();
    myChart9.resize();
}, 1000);