
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="./img/favicon.ico" />
    <!-- <link rel="stylesheet" href="./fonts/font.css" /> -->

    <link rel="stylesheet" href="./css/bootstrap.min.css" />
    <link rel="stylesheet" href="./css/main.css" />
    <link rel="stylesheet" href="./css/jq22.css" />
    <title>智慧水产养殖监管平台</title>
    <style>
      .BMap_scaleTxt{
        color: #fff!important;
      }

      /* Loading animation */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .main .content > div {
        animation: fadeInUp 0.8s ease-out;
      }

      .main .content .leftBox {
        animation-delay: 0.1s;
      }

      .main .content .middleBox {
        animation-delay: 0.2s;
      }

      .main .content .rightBox {
        animation-delay: 0.3s;
      }

      /* Pulse effect for important metrics */
      .animText {
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }

      /* Subtle glow for charts */
      [id^="echarts"] {
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.5);
      }

      /* Light theme specific adjustments */
      .main .header img {
        filter: brightness(0.8) contrast(1.2);
      }

      /* Floating panel animations */
      .main .content .leftBox,
      .main .content .rightBox,
      .main .content .middleBox {
        animation: floatIn 1s ease-out;
      }

      @keyframes floatIn {
        from {
          opacity: 0;
          transform: translateY(50px) scale(0.9);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      /* Map overlay gradient */
      .main .content .fullPageMap::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at center, transparent 30%, rgba(227, 242, 253, 0.1) 100%);
        pointer-events: none;
        z-index: 2;
      }
    </style>
  </head>
  <body>
    <div class="main" id="appIndex">
      <div class="header">
        <img
          style="width: 80px;height: 84px;margin-right: 20px;"
          src="img/logo.png"
          alt="更多资源：https://gitee.com/iGaoWei/big-data-view"
        />
        <img src="img/aquaculture_title.png" alt="智慧水产养殖监管平台" />
        <div class="dateBox">
          <span id="time"
            >{{ year }}-{{ month }}-{{ date }}&nbsp;&nbsp; {{ hour }}:{{
              minute
            }}:{{ second }}&nbsp;&nbsp; {{ strDate }}
            <!-- <img
              style="
            width: 25px;
            height: 16px;
            margin: 0 5px 0 10px;
        "
              :src="weather_icon"
              alt="天气"
            />
            {{ weather_curr }}</span
          > -->
          <!-- <span>dsggdfgf</span>
             <span>dsggdfgf</span> -->
        </div>
      </div>

      <div class="content">
        <!-- Full-page Map -->
        <div class="fullPageMap">
          <div id="container"></div>
        </div>
        <!-- Floating Left Panel -->
        <div class="leftBox">
          <div class="pingfen">
            <div>
              <div class="pfTitle">
                <img src="img/water_quality.png" alt="水质评分" />
                <span class="titleText">水质评分</span>
              </div>
              <div class="pfcontent">
                <div class="leftBox">
                  <img src="img/water_grade.png" alt="水质等级" />
                  <span>水质等级</span>
                </div>
                <div id="echartAqpf" style="width:105px;height:105px;padding-left: 20px;margin-bottom: 32px;"></div>
              </div>
            </div>
          </div>
          <div class="paimin">
            <div>
              <div class="pfTitle" style="margin-bottom: 30px;">
                <img style="margin-right: 10px;" src="img/farm_ranking.png" alt="养殖场排名" />
                <span class="titleText">养殖场水质排名</span>
              </div>
              <div id="FontScroll">
                <ul>
                  <li>
                    <a href="#">
                      <span class="leftSpan">蓝海水产养殖基地 </span>
                      <span>95</span>
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <span class="leftSpan">东方渔业养殖场 </span>
                      <span>92</span>
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <span class="leftSpan">海丰水产合作社 </span>
                      <span>89</span>
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <span class="leftSpan">绿波养殖专业合作社 </span>
                      <span>87</span>
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <span class="leftSpan">金鱼湖生态养殖场 </span>
                      <span>85</span>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="top10">
            <div>
              <div class="pfTitle" style="margin-bottom: 30px;">
                <img style="margin-right: 10px;" src="img/env_alerts.png" alt="环境预警" />
                <span class="titleText">环境预警TOP10</span>
              </div>
              <div id="echartsEnvAlerts" style="width:100%;height:270px;"></div>
            </div>
          </div>
        </div>

        <!-- Floating Bottom Panel -->
        <div class="middleBox">
          <div class="tongjiList">
            <div>
              <div class="col-md-3" style="margin: 0 15px;position: relative;">
                <div style="justify-content: center;position: relative;top: 22px;" class="pfTitle">
                  <img style="margin-right: 10px;" src="img/water_system.png" alt="水循环系统" />
                  <span class="titleText">水循环系统</span>
                </div>
                <!-- <img src="img/xfjsxt.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" /> -->
                <div id="echartsxfjs" style="width:100%;height:130px;"></div>
              </div>
              <div class="comLine"></div>
              <div class="col-md-3" style="margin: 0 15px;position: relative;">
                <div style="justify-content: center;position: relative; top: 22px;
            " class="pfTitle">
                  <img style="margin-right: 10px;" src="img/water_alerts.png" alt="水质预警统计" />
                  <span class="titleText">水质预警统计</span>
                </div>
                <span style="top:90px" class="smdate">{{ year }}/{{ month }}</span>
                <div id="echartsldbj" style="width:100%;height:130px;margin-top: 10px;"></div>
                <!-- <img src="img/ldbjtj.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" /> -->
                <!-- <span class="spanfont"
                  >11 &nbsp; 12 &nbsp; 13 &nbsp;11 &nbsp;12 &nbsp;13 &nbsp;
                  15</span
                > -->
              </div>
              <div class="comLine"></div>
              <div class="col-md-3" style="margin: 0 15px;position: relative;">
                <div style="justify-content: center;position: relative; top: 22px;" class="pfTitle">
                  <img style="margin-right: 10px;" src="img/feed_alerts.png" alt="饲料管理预警" />
                  <span class="titleText">饲料管理预警</span>
                </div>
                <span style="top:90px" class="smdate">{{ year }}/{{ month }}</span>
                <!-- <img src="img/jgbjtj.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" /> -->
                <div id="echartsjgbj" style="width:100%;height:130px;"></div>
                <!-- <span class="spanfont"
                  >11 &nbsp; 12 &nbsp; 13 &nbsp;11 &nbsp;12 &nbsp;13 &nbsp;
                  15</span
                > -->
              </div>
              <div class="comLine"></div>
              <div class="col-md-3" style="margin: 0 15px;position: relative;">
                <div
                  style="justify-content: center;margin-bottom: 3px;margin-top: 30px;"
                  class="pfTitle"
                >
                  <img style="margin-right: 10px;" src="img/daily_monitor.png" alt="日常监测统计" />
                  <span class="titleText">日常监测统计</span>
                </div>
                <span style="left: 120px;bottom:130px" class="smdate"
                >{{ year }}/{{ month }}</span
              >
                <div id="echartsrcxj" style="width:100%;height:130px;"></div>
                <!--
                <span style="left: 110px;" class="smdate"
                  >{{ year }}/{{ month }}</span
                > -->
                <!-- <img src="img/rcxjtj.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" /> -->

                <!-- <span class="spanfont"
                  >11 &nbsp;&nbsp;12  &nbsp;&nbsp;13  &nbsp;&nbsp;11 &nbsp;&nbsp;12 &nbsp;&nbsp;13&nbsp;&nbsp;15
                  &nbsp;&nbsp;15
                </span>
                <div style="display: flex;justify-content: center;">
                  <img src="img/zflft.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" />
                  <span class="smtext">巡查数量</span>
                  <img src="img/zfright.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" />
                  <span class="smtext">巡查覆盖率</span>
                </div> -->
              </div>
            </div>
          </div>
        </div>

        <!-- Floating Right Panel -->
        <div class="rightBox">
          <div class="weihutj">
            <div>
              <div style="margin-left: 27px;" class="pfTitle">
                <img style="margin-right: 10px;" src="img/equipment_maintenance.png" alt="设备维护统计" />
                <span class="titleText">设备维护统计</span>
              </div>
              <div class="pfcontent flexrow">
                <div
                  style="width: 120px;"
                  class="leftBox flexcolom"
                >
                  <div class="sbwx">
                    <span class="sbxanim animText">69</span>
                    <span class="smtext sbwxtext">设备维修</span>
                  </div>
                  <span style="margin-top: 20px;" class="smtext">维护次数</span>
                </div>

                <div class="flexcolom" style="margin: 0 8px;">
                 <div id="echartWbtj" style="width:100px;height:100px;"></div>
                  <span style="margin-top: 20px;" class="smtext"
                    >维护覆盖率</span
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="guzhangtj">
            <div>
              <div style="margin: 0 15px;position: relative;">
                <div
                  style="justify-content: flex-start;margin-bottom: 3px;"
                  class="pfTitle"
                >
                  <img style="margin-right: 10px;" src="img/system_failure.png" alt="系统故障统计" />
                  <span class="titleText">系统故障统计</span>
                </div>
                <span style="left: 40%;top: 30%;" class="smdate"
                  >{{ year }}/{{ month }}</span
                >
                <div id="echartsgztj" style="width:100%;height:130px;"></div>
                <!-- <img src="img/gztjs.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" />
                <span class="spanfont"
                  >11 &nbsp; 12 &nbsp; 13 &nbsp;11 &nbsp;12 &nbsp;13 &nbsp;
                  15</span
                > -->
                <!-- <div
                  style="display: flex;justify-content: center;margin-top: 10px;"
                >
                  <img src="img/zflft.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" />
                  <span class="smtext">故障数量</span>
                  <img src="img/zfright.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" />
                  <span class="smtext">故障处理及时率</span>
                </div> -->
              </div>
            </div>
          </div>
          <div class="huojingtj">
            <div>
              <div style="margin: 0 15px;position: relative;">
                <div
                  style="justify-content: flex-start;margin-bottom: 3px;"
                  class="pfTitle"
                >
                  <img style="margin-right: 10px;" src="img/env_emergency.png" alt="环境应急统计" />
                  <span class="titleText">环境应急统计</span>
                </div>
                <span style="left: 40%;top: 30%;"  class="smdate"
                  >{{ year }}/{{ month }}</span
                >
                <div id="echartshjtj" style="width:100%;height:130px;left: 10px;"></div>
                <!-- <img src="img/hjtjs.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" />
                <span class="spanfont"
                  >11 &nbsp; 12 &nbsp; 13 &nbsp;11 &nbsp;12 &nbsp;13 &nbsp;
                  15</span
                >
                <div
                  style="display: flex;justify-content: center;margin-top: 10px;"
                >
                  <img src="img/zflft.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" />
                  <span class="smtext">火警数量</span>
                  <img src="img/zfright.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" />
                  <span class="smtext">火警处理及时率</span>
                </div> -->
              </div>
            </div>
          </div>
          <div class="zhifatj">
            <div class="flexcolom">
                <div
                  class="pfTitle"
                >
                  <img style="margin-right: 10px;" src="img/compliance.png" alt="合规监管统计" />
                  <span class="titleText">合规监管统计</span>
                </div>
                <div id="echartszfjgtj" style="width:100%;height:130px;padding: 0 12px;top:-18px"></div>
                <!-- <img src="img/zfjgtj.png" alt="更多资源：https://gitee.com/iGaoWei/big-data-view" /> -->
              </div>
          </div>
        </div>
      </div>
    </div>

    <script src="js/jquery.min.js"></script>
    <script src="js/vue.min.js"></script>
    <script src="js/echarts.min.js"></script>
    <script src="js/walden.js"></script>
    <script src="js/roma.js"></script>
    <script src="js/jquery.countUp.js"></script>
    <script
      type="text/javascript"
      src="https://api.map.baidu.com/api?v=2.0&ak=2YCQxbGampc7M1pFsKWNiwpS5F7RKVRt&s=1"
    ></script>
    <script src="js/fontscroll.js"></script>
    <script src="js/main.js"></script>
    <script src="js/echart.js"></script>
    <script type="text/javascript">
     var markerArr = [

{
  point: "103.850585,36.069335",
},

{
   point: "103.86812,36.063267",
 },
 {
   point: "103.856047,36.042024",
 },
 {
  point: "103.841674,36.067235",
},

{
   point: "103.953207,36.031751",
 },
 {
   point: "103.801573,36.073886",
 },
 {
  point: "103.819108,36.068168",
},

{
   point: "103.715954,36.099723",
 },
 {
   point: "103.716573,36.101553",
 },
 {
  point: "103.708129,36.100725",
},

{
   point: "103.712378,36.10171",
 },
 {
   point: "103.711467,36.103415",
 },
 {
  point: "103.70226,36.102388",
},

{
   point: "103.714764,36.100639",
 },
 {
   point: "103.700535,36.110261",
 }

];

var markerArr1 = [

{
  point: "103.865245,36.061166",
},

{
   point: "103.773259,36.077037",
 },
 {
   point: "103.910664,36.05323",
 },
 {
  point: "103.768085,36.108768",
},

{
   point: "103.689896,36.107835",
 },
 {
   point: "103.841099,36.074236",
 },
 {
  point: "103.839949,36.056965",
},

{
   point: "103.886517,36.065368",
 },
 {
   point: "103.815228,36.091504",
 },
 {
  point: "103.870995,36.026147",
},

];

var markerArr2 = [

{
  point: "103.784757,36.059533",
},

{
   point: "103.754862,36.067235",
 },
 {
   point: "103.779008,36.135589",
 },
 {
  point: "103.839374,36.095704",
},

{
   point: "103.693633,36.116932",
 },
 {
   point: "103.946021,36.034553",
 },
 {
  point: "103.967868,36.00466",
},

{
   point: "103.679835,36.114833",
 },
 {
   point: "103.977641,36.055564",
 },
 {
  point: "103.856334,36.060233",
},

];
      // Initialize map after DOM is ready
      function initMap() {
        var map = new BMap.Map('container')
        // 创建地图实例
        var point = new BMap.Point(103.71878, 36.10396)
        // 创建点坐标
        map.centerAndZoom(point, 15)
        // 初始化地图，设置中心点坐标和地图级别
        map.enableScrollWheelZoom(true)
      var ctrlSca = new window.BMap.ScaleControl({
                                                anchor: BMAP_ANCHOR_BOTTOM_RIGHT
                                            });
                                           map.addControl(ctrlSca);
      for(var i = 0; i < markerArr.length; i++) {
                                                      var p0 = markerArr[i].point.split(",")[0];
                                                      var p1 = markerArr[i].point.split(",")[1];
                                                      var maker = addMarker(new window.BMap.Point(p0, p1), i);

                                             }

                                             for(var i = 0; i < markerArr1.length; i++) {
                                                      var p0 = markerArr1[i].point.split(",")[0];
                                                      var p1 = markerArr1[i].point.split(",")[1];
                                                      var maker = addMarker1(new window.BMap.Point(p0, p1), i);

                                             }
                                             for(var i = 0; i < markerArr2.length; i++) {
                                                      var p0 = markerArr2[i].point.split(",")[0];
                                                      var p1 = markerArr2[i].point.split(",")[1];
                                                      var maker = addMarker2(new window.BMap.Point(p0, p1), i);

                                             }


// 添加标注
                                            function addMarker(point, index) {
                                                         var myIcon = new BMap.Icon("img/green5.png",
                                                               new BMap.Size(23, 25), {
                                                                        // offset: new BMap.Size(10, 25),
                                                                        //  imageOffset: new BMap.Size(0, 0 - index * 25)
                                                                 });
                                                                var marker = new BMap.Marker(point, {
                                                                      icon: myIcon
                                                                   });
                                                                  map.addOverlay(marker);
                                                                    return marker;
                                                }

                                            function addMarker1(point, index) {
                                                         var myIcon = new BMap.Icon("img/red5.png",
                                                               new BMap.Size(23, 25), {
                                                                        // offset: new BMap.Size(10, 25),
                                                                        //  imageOffset: new BMap.Size(0, 0 - index * 25)
                                                                 });
                                                                var marker = new BMap.Marker(point, {
                                                                      icon: myIcon
                                                                   });
                                                                  map.addOverlay(marker);
                                                                    return marker;
                                                }


                                            function addMarker2(point, index) {
                                                         var myIcon = new BMap.Icon("img/yellow5.png",
                                                               new BMap.Size(23, 25), {
                                                                        // offset: new BMap.Size(10, 25),
                                                                        //  imageOffset: new BMap.Size(0, 0 - index * 25)
                                                                 });
                                                                var marker = new BMap.Marker(point, {
                                                                      icon: myIcon
                                                                   });
                                                                  map.addOverlay(marker);
                                                                    return marker;
                                                }



        map.setMapStyle({ style: 'midnight' })
        $('#container').css('background-color', 'rgba(50,72,106,0.2)')

        return map;
      }

      // Initialize map when page loads
      $(document).ready(function() {
        setTimeout(function() {
          if (typeof BMap !== 'undefined') {
            initMap();
          } else {
            console.error('Baidu Maps API not loaded');
          }
        }, 1000);
      });

    </script>

  </body>
</html>
